import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{r}from"./chunk-D4RADZKF-DNKU_WG6.js";function u({children:s,heading:n,type:i}){const{type:t,close:o}=x(),a=i===t;return r.useEffect(()=>{const d=new AbortController;return a&&document.addEventListener("keydown",function(l){l.key==="Escape"&&o()},{signal:d.signal}),()=>d.abort()},[o,a]),e.jsxs("div",{"aria-modal":!0,className:`overlay ${a?"expanded":""}`,role:"dialog",children:[e.jsx("button",{className:"close-outside",onClick:o}),e.jsxs("aside",{children:[e.jsxs("header",{children:[e.jsx("h3",{children:n}),e.jsx("button",{className:"close reset",onClick:o,children:"✕"})]}),e.jsx("main",{children:s})]})]})}const c=r.createContext(null);u.Provider=function({children:n}){const[i,t]=r.useState("closed");return e.jsx(c.Provider,{value:{type:i,open:t,close:()=>t("closed")},children:n})};function x(){const s=r.useContext(c);if(!s)throw new Error("useAside must be used within an AsideProvider");return s}export{u as A,x as u};
