import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction, Await, Link} from 'react-router';
import {
  getSelectedProductOptions,
  Analytics,
  useOptimisticVariant,
  getProductOptions,
  getAdjacentAndFirstAvailableVariants,
  useSelectedOptionInUrlParam,
} from '@shopify/hydrogen';
import {ProductPrice} from '~/components/ProductPrice';
import {ProductHeroImage, ProductThumbnailImage} from '~/components/ProductImage';
import {ProductForm} from '~/components/ProductForm';
import {ProductUpsellCrosssell} from '~/components/ProductUpsellCrosssell';
import {CrossSellProducts} from '~/components/CrossSellProducts';
import {redirectIfHandleIsLocalized} from '~/lib/redirect';
import React, {Suspense} from 'react';

export const meta: MetaFunction<typeof loader> = ({data}) => {
  return [
    {title: `Hydrogen | ${data?.product.title ?? ''}`},
    {
      rel: 'canonical',
      href: `/products/${data?.product.handle}`,
    },
  ];
};

export async function loader(args: LoaderFunctionArgs) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return {...deferredData, ...criticalData};
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 */
async function loadCriticalData({
  context,
  params,
  request,
}: LoaderFunctionArgs) {
  const {handle} = params;
  const {storefront} = context;

  if (!handle) {
    throw new Error('Expected product handle to be defined');
  }

  const [{product}] = await Promise.all([
    storefront.query(PRODUCT_QUERY, {
      variables: {handle, selectedOptions: getSelectedProductOptions(request)},
      cache: storefront.CacheLong(), // Aggressive caching for product data
    }),
    // Add other queries here, so that they are loaded in parallel
  ]);

  if (!product?.id) {
    throw new Response(null, {status: 404});
  }

  // The API handle might be localized, so redirect to the localized handle
  redirectIfHandleIsLocalized(request, {handle, data: product});

  return {
    product,
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 */
function loadDeferredData({context, params}: LoaderFunctionArgs) {
  const {storefront} = context;

  // Fetch related products for upsell/cross-sell
  const relatedProducts = storefront
    .query(RELATED_PRODUCTS_QUERY, {
      variables: {first: 20},
      cache: storefront.CacheLong(),
    })
    .catch((error) => {
      console.error('Error fetching related products:', error);
      return {products: {nodes: []}};
    });

  return {
    relatedProducts,
  };
}

export default function Product() {
  const {product, relatedProducts} = useLoaderData<typeof loader>();

  // State for selected image
  const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);

  // Optimistically selects a variant with given available variant information
  const selectedVariant = useOptimisticVariant(
    product.selectedOrFirstAvailableVariant,
    getAdjacentAndFirstAvailableVariants(product),
  );

  // Sets the search param to the selected variant without navigation
  // only when no search params are set in the url
  useSelectedOptionInUrlParam(selectedVariant.selectedOptions);

  // Get the product options array
  const productOptions = getProductOptions({
    ...product,
    selectedOrFirstAvailableVariant: selectedVariant,
  });

  const {title, descriptionHtml} = product;

  return (
    <div className="min-h-screen product-page-content" style={{
      backgroundImage: 'url(/newhomepage/mobile_homeage_bg_sf.webp), url(/newhomepage/mobile_homeage_bg_sf.png)',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }}>
      {/* Modern Hero Section */}
      <div className="relative bg-gradient-to-br from-[#3a5c5c]/95 to-[#2d4747]/95 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">

            {/* Enhanced Product Images Section */}
            <div className="space-y-6">
              {product.images?.nodes?.length > 0 ? (
                <>
                  {/* Main Product Image */}
                  <div className="relative group">
                    <div className="aspect-square bg-white/10 rounded-3xl overflow-hidden backdrop-blur-sm border border-white/20 shadow-2xl">
                      <ProductHeroImage
                        image={product.images.nodes[selectedImageIndex] || product.images.nodes[0]}
                      />
                    </div>

                    {/* Enhanced Origin Badge */}
                    <div className="absolute top-6 left-6 bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                      <div className="flex items-center space-x-2">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        <span>Nicaragua Origin</span>
                      </div>
                    </div>

                    {/* Quality Badge */}
                    <div className="absolute top-6 right-6 bg-army-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                      Premium Quality
                    </div>
                  </div>

                  {product.images.nodes.length > 1 && (
                    <div className="grid grid-cols-4 gap-3">
                      {product.images.nodes.map((image, index) => (
                        <div
                          key={image.id}
                          className={`aspect-square bg-white/10 rounded-lg overflow-hidden backdrop-blur-sm border transition-all duration-300 cursor-pointer ${
                            selectedImageIndex === index
                              ? 'border-[#db8027] border-2 ring-2 ring-[#db8027]/50'
                              : 'border-white/20 hover:border-white/40'
                          }`}
                          onClick={() => setSelectedImageIndex(index)}
                        >
                          <ProductThumbnailImage
                            image={image}
                            isActive={selectedImageIndex === index}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="aspect-square bg-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-white/60 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-white/60">No image available</span>
                  </div>
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="space-y-8">
              {/* Modern Breadcrumb */}
              <nav className="flex items-center space-x-2 text-sm">
                <Link to="/collections/all" className="text-white/70 hover:text-[#db8027] transition-colors font-medium">
                  Coffee Collection
                </Link>
                <svg className="w-4 h-4 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-white font-medium">{title}</span>
              </nav>

              {/* Enhanced Product Title and Details */}
              <div className="space-y-6">
                <div>
                  <p className="text-[#db8027] mb-3 uppercase tracking-wider text-sm font-bold">{product.vendor}</p>
                  <h1 className="text-4xl lg:text-6xl font-bold leading-tight mb-6 text-white">{title}</h1>

                  {/* Enhanced Status Badges */}
                  <div className="flex flex-wrap gap-3 mb-6">
                    <div className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-bold flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>In Stock</span>
                    </div>
                    <div className="bg-[#db8027] text-white px-4 py-2 rounded-full text-sm font-bold flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                      </svg>
                      <span>Free Shipping</span>
                    </div>
                    <div className="bg-white/20 text-white px-4 py-2 rounded-full text-sm font-bold backdrop-blur-sm">
                      Premium Roast
                    </div>
                    <div className="bg-white/20 text-white px-4 py-2 rounded-full text-sm font-bold backdrop-blur-sm">
                      Single Origin
                    </div>
                  </div>
                </div>

                {/* Enhanced Price Display */}
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-white/70 mb-1">Price per bag</div>
                      <div className="text-4xl lg:text-5xl font-bold text-[#db8027]">
                        <ProductPrice
                          price={selectedVariant?.price}
                          compareAtPrice={selectedVariant?.compareAtPrice}
                        />
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-white/70 mb-1">You save</div>
                      <div className="text-2xl font-bold text-green-400">Up to 25%</div>
                      <div className="text-xs text-white/60">with bundles + subscription</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Product Form */}
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-2xl">
                <ProductForm
                  productOptions={productOptions}
                  selectedVariant={selectedVariant}
                  product={product}
                />
              </div>

              {/* Quick Features */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-[#db8027] rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-white">Expert Roasted</p>
                    <p className="text-sm text-white/70">Small batch perfection</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-[#db8027] rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-white">Ethically Sourced</p>
                    <p className="text-sm text-white/70">Direct trade partners</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Description Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-[#3a5c5c] mb-6 text-center">About This Coffee</h2>
          <div className="w-16 h-1 bg-[#db8027] mx-auto mb-8"></div>

          <div
            className="text-gray-700 leading-relaxed prose prose-lg max-w-none text-center"
            dangerouslySetInnerHTML={{__html: descriptionHtml}}
          />
        </div>
      </div>

      {/* Adventure Promise Section */}
      <div className="py-20 bg-gradient-to-r from-[#3a5c5c] to-[#2d4747]">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">Our Adventure Promise</h2>
            <div className="w-20 h-1 bg-[#db8027] mx-auto mb-6"></div>
            <p className="adventure-promise-text text-white/80 text-lg max-w-2xl mx-auto px-4 text-center">
              Every cup is crafted to fuel your next adventure, sourced with care and roasted to perfection.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Ethically Sourced</h3>
              <p className="text-white/70">Direct partnerships with mountain farmers in Nicaragua, ensuring fair trade and sustainable practices.</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Expert Roasted</h3>
              <p className="text-white/70">Small-batch roasting by master craftsmen, bringing out the unique flavors of each origin.</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Adventure Ready</h3>
              <p className="text-white/70">Perfectly crafted to fuel any adventure, from mountain peaks to morning commutes.</p>
            </div>
          </div>

          <div className="text-center">
            <Link
              to="/collections/all"
              className="inline-flex items-center px-8 py-4 bg-[#db8027] hover:bg-[#c4721f] text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              Explore All Coffee
              <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </div>
      </div>

      {/* Enhanced Bundle Upsell Section */}
      <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white mb-12">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">🎁 Upgrade to Bundles & Save Big!</h2>
            <p className="text-xl lg:text-2xl opacity-90 mb-4">Get more variety, better value, and never run out of great coffee</p>
            <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-6 py-3">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
              </svg>
              <span className="text-lg font-bold">Save up to 25% with bundles + subscription</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Roasters Box */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 relative overflow-hidden">
              <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                BEST VALUE
              </div>
              <div className="flex items-start space-x-6">
                <div className="w-24 h-24 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-4xl">📦</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-3">Roasters Box</h3>
                  <p className="text-white/90 mb-4">3 different premium coffee varieties delivered monthly. Perfect for exploring new flavors!</p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2 text-white/80">
                      <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>3 premium coffee bags per month</span>
                    </div>
                    <div className="flex items-center space-x-2 text-white/80">
                      <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Save 20% vs buying individually</span>
                    </div>
                    <div className="flex items-center space-x-2 text-white/80">
                      <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Free shipping included</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <div className="text-3xl font-bold text-white">$49.99</div>
                      <div className="text-sm text-white/70">$16.66 per bag</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-400">Save $15</div>
                      <div className="text-sm text-white/70">vs individual purchase</div>
                    </div>
                  </div>

                  <Link
                    to="/products/roasters-box"
                    className="w-full bg-white text-[#db8027] font-bold py-4 px-6 rounded-xl text-center hover:bg-gray-100 transition-colors block"
                  >
                    Upgrade to Roasters Box
                  </Link>
                </div>
              </div>
            </div>

            {/* Blend Box */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 relative overflow-hidden">
              <div className="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                POPULAR
              </div>
              <div className="flex items-start space-x-6">
                <div className="w-24 h-24 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                  <span className="text-4xl">🎯</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-3">Blend Box</h3>
                  <p className="text-white/90 mb-4">Curated selection of our signature blends. Perfect for finding your favorite!</p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2 text-white/80">
                      <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>2 signature blend bags</span>
                    </div>
                    <div className="flex items-center space-x-2 text-white/80">
                      <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Save 15% vs individual bags</span>
                    </div>
                    <div className="flex items-center space-x-2 text-white/80">
                      <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Perfect for gift giving</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <div className="text-3xl font-bold text-white">$39.99</div>
                      <div className="text-sm text-white/70">$19.99 per bag</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-400">Save $8</div>
                      <div className="text-sm text-white/70">vs individual purchase</div>
                    </div>
                  </div>

                  <Link
                    to="/collections/all"
                    className="w-full bg-white text-[#db8027] font-bold py-4 px-6 rounded-xl text-center hover:bg-gray-100 transition-colors block"
                  >
                    Get Blend Box
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cross-sell Section - 4 Products (K-cups + 3 Random) */}
      <div className="bg-[#eeedc1] py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl lg:text-5xl font-bold text-[#3a5c5c] mb-6">☕ You Might Also Love</h2>
            <p className="text-xl text-[#3a5c5c]/80">Discover more amazing flavors from our collection</p>
          </div>

          <Suspense fallback={
            <div className="text-center py-8">
              <div className="inline-flex items-center space-x-2 text-[#3a5c5c]">
                <svg className="animate-spin h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-lg">Loading recommendations...</span>
              </div>
            </div>
          }>
            <Await resolve={relatedProducts}>
              {(resolvedProducts) => (
                <CrossSellProducts
                  currentProduct={product}
                  allProducts={resolvedProducts?.products?.nodes || []}
                />
              )}
            </Await>
          </Suspense>
        </div>
      </div>

      <Analytics.ProductView
        data={{
          products: [
            {
              id: product.id,
              title: product.title,
              price: selectedVariant?.price.amount || '0',
              vendor: product.vendor,
              variantId: selectedVariant?.id || '',
              variantTitle: selectedVariant?.title || '',
              quantity: 1,
            },
          ],
        }}
      />
    </div>
  );
}

const PRODUCT_VARIANT_FRAGMENT = `#graphql
  fragment ProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

const PRODUCT_FRAGMENT = `#graphql
  fragment Product on Product {
    id
    title
    vendor
    handle
    descriptionHtml
    description
    encodedVariantExistence
    encodedVariantAvailability
    requiresSellingPlan
    images(first: 10) {
      nodes {
        id
        url
        altText
        width
        height
      }
    }
    sellingPlanGroups(first: 10) {
      nodes {
        name
        options {
          name
          values
        }
        sellingPlans(first: 10) {
          nodes {
            id
            name
            description
            options {
              name
              value
            }
            priceAdjustments {
              adjustmentValue {
                ... on SellingPlanFixedAmountPriceAdjustment {
                  adjustmentAmount {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanFixedPriceAdjustment {
                  price {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanPercentagePriceAdjustment {
                  adjustmentPercentage
                }
              }
              orderCount
            }
            recurringDeliveries
            checkoutCharge {
              type
              value {
                ... on SellingPlanCheckoutChargePercentageValue {
                  percentage
                }
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
              }
            }
          }
        }
      }
    }
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...ProductVariant
        }
        swatch {
          color
          image {
            previewImage {
              url
            }
          }
        }
      }
    }
    selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
      ...ProductVariant
    }
    adjacentVariants (selectedOptions: $selectedOptions) {
      ...ProductVariant
    }
    seo {
      description
      title
    }
  }
  ${PRODUCT_VARIANT_FRAGMENT}
` as const;

const PRODUCT_QUERY = `#graphql
  query Product(
    $country: CountryCode
    $handle: String!
    $language: LanguageCode
    $selectedOptions: [SelectedOptionInput!]!
  ) @inContext(country: $country, language: $language) {
    product(handle: $handle) {
      ...Product
    }
  }
  ${PRODUCT_FRAGMENT}
` as const;

const RELATED_PRODUCTS_QUERY = `#graphql
  fragment RelatedProduct on Product {
    id
    title
    handle
    availableForSale
    tags
    featuredImage {
      id
      url
      altText
      width
      height
    }
    priceRange {
      minVariantPrice {
        amount
        currencyCode
      }
    }
    variants(first: 1) {
      nodes {
        id
        availableForSale
        selectedOptions {
          name
          value
        }
        price {
          amount
          currencyCode
        }
      }
    }
  }
  query RelatedProducts(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
  ) @inContext(country: $country, language: $language) {
    products(first: $first, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...RelatedProduct
      }
    }
  }
` as const;
