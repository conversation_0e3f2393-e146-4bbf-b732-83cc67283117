import{w as r}from"./with-props-ZU_SrDpf.js";import{j as i}from"./jsx-runtime-D5QEUsP9.js";import{u as o,L as t}from"./chunk-D4RADZKF-DNKU_WG6.js";const d=r(function(){const{policies:s}=o();return i.jsxs("div",{className:"policies",children:[i.jsx("h1",{children:"Policies"}),i.jsx("div",{children:s.map(e=>e?i.jsx("fieldset",{children:i.jsx(t,{to:`/policies/${e.handle}`,children:e.title})},e.id):null)})]})});export{d as default};
