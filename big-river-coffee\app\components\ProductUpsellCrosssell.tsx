import {Link} from 'react-router';
import {Money, Image} from '@shopify/hydrogen';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment} from 'storefrontapi.generated';

interface UpsellCrosssellProps {
  currentProduct: ProductFragment;
  allProducts?: any[]; // This would come from a loader or context
}

export function ProductUpsellCrosssell({currentProduct, allProducts = []}: UpsellCrosssellProps) {
  const {open} = useAside();

  // Bundle products for upselling
  const bundleProducts = allProducts.filter(product => 
    product.title.toLowerCase().includes('box') || 
    product.title.toLowerCase().includes('bundle') ||
    product.tags?.some((tag: string) => tag.toLowerCase().includes('bundle'))
  );

  // Related coffee products for cross-selling (exclude current product)
  const relatedProducts = allProducts.filter(product => 
    product.id !== currentProduct.id &&
    !product.title.toLowerCase().includes('box') &&
    !product.title.toLowerCase().includes('bundle') &&
    (product.title.toLowerCase().includes('coffee') || 
     product.tags?.some((tag: string) => tag.toLowerCase().includes('coffee')))
  ).slice(0, 4);

  // K-cup products for cross-selling
  const kcupProducts = allProducts.filter(product =>
    product.title.toLowerCase().includes('k-cup') ||
    product.title.toLowerCase().includes('kcup') ||
    product.tags?.some((tag: string) => tag.toLowerCase().includes('k-cup'))
  ).slice(0, 3);

  return (
    <div className="space-y-12">
      {/* Upsell Section - Bundle Products */}
      {bundleProducts.length > 0 && (
        <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">🎁 Upgrade Your Coffee Experience</h2>
            <p className="text-xl opacity-90">Get more variety and save with our curated bundles</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {bundleProducts.slice(0, 2).map((product) => (
              <div key={product.id} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-start space-x-4">
                  {product.featuredImage && (
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        data={product.featuredImage}
                        aspectRatio="1/1"
                        sizes="80px"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">{product.title}</h3>
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-2xl font-bold">
                        <Money data={product.priceRange.minVariantPrice} />
                      </div>
                      <div className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                        Best Value
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/products/${product.handle}`}
                        className="flex-1 bg-white text-[#db8027] font-medium py-2 px-4 rounded-lg text-center hover:bg-gray-100 transition-colors"
                      >
                        View Details
                      </Link>
                      <AddToCartButton
                        disabled={!product.availableForSale}
                        onClick={() => open('cart')}
                        lines={
                          product.variants?.nodes?.[0]
                            ? [
                                {
                                  merchandiseId: product.variants.nodes[0].id,
                                  quantity: 1,
                                  selectedVariant: product.variants.nodes[0],
                                },
                              ]
                            : []
                        }
                        className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Add to Cart
                      </AddToCartButton>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Cross-sell Section - Related Coffee Products */}
      {relatedProducts.length > 0 && (
        <div className="bg-[#eeedc1] rounded-2xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-[#3a5c5c] mb-4">☕ You Might Also Love</h2>
            <p className="text-xl text-[#3a5c5c]/80">Discover more amazing flavors from our collection</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts.map((product) => (
              <div key={product.id} className="bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-shadow">
                {product.featuredImage && (
                  <div className="aspect-square rounded-lg overflow-hidden mb-4">
                    <Image
                      data={product.featuredImage}
                      aspectRatio="1/1"
                      sizes="(max-width: 640px) 50vw, (max-width: 1024px) 25vw, 20vw"
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                )}
                <h3 className="font-semibold text-[#3a5c5c] mb-2 line-clamp-2">{product.title}</h3>
                <div className="text-xl font-bold text-[#db8027] mb-4">
                  <Money data={product.priceRange.minVariantPrice} />
                </div>
                <div className="flex space-x-2">
                  <Link
                    to={`/products/${product.handle}`}
                    className="flex-1 bg-[#3a5c5c] text-white font-medium py-2 px-3 rounded-lg text-center text-sm hover:bg-[#2d4747] transition-colors"
                  >
                    View
                  </Link>
                  <AddToCartButton
                    disabled={!product.availableForSale}
                    onClick={() => open('cart')}
                    lines={
                      product.variants?.nodes?.[0]
                        ? [
                            {
                              merchandiseId: product.variants.nodes[0].id,
                              quantity: 1,
                              selectedVariant: product.variants.nodes[0],
                            },
                          ]
                        : []
                    }
                    className="flex-1 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-2 px-3 rounded-lg text-sm transition-colors"
                  >
                    Add
                  </AddToCartButton>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Cross-sell Section - K-Cup Products */}
      {kcupProducts.length > 0 && (
        <div className="bg-gradient-to-r from-[#5d8e8e] to-[#4a7373] rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">🚀 Quick & Convenient</h2>
            <p className="text-xl opacity-90">Try our K-Cup pods for instant coffee perfection</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {kcupProducts.map((product) => (
              <div key={product.id} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                {product.featuredImage && (
                  <div className="aspect-square rounded-lg overflow-hidden mb-4">
                    <Image
                      data={product.featuredImage}
                      aspectRatio="1/1"
                      sizes="(max-width: 768px) 100vw, 33vw"
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <h3 className="font-semibold text-lg mb-2">{product.title}</h3>
                <div className="text-2xl font-bold mb-4">
                  <Money data={product.priceRange.minVariantPrice} />
                </div>
                <div className="flex space-x-2">
                  <Link
                    to={`/products/${product.handle}`}
                    className="flex-1 bg-white/20 hover:bg-white/30 text-white font-medium py-2 px-4 rounded-lg text-center transition-colors"
                  >
                    View Details
                  </Link>
                  <AddToCartButton
                    disabled={!product.availableForSale}
                    onClick={() => open('cart')}
                    lines={
                      product.variants?.nodes?.[0]
                        ? [
                            {
                              merchandiseId: product.variants.nodes[0].id,
                              quantity: 1,
                              selectedVariant: product.variants.nodes[0],
                            },
                          ]
                        : []
                    }
                    className="flex-1 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-2 px-4 rounded-lg transition-colors"
                  >
                    Add to Cart
                  </AddToCartButton>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Call-to-Action for More Products */}
      <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
        <h2 className="text-2xl font-bold text-[#3a5c5c] mb-4">Explore Our Full Collection</h2>
        <p className="text-gray-600 mb-6">Discover all our premium coffee products and find your perfect match</p>
        <Link
          to="/collections/all"
          className="inline-block bg-army-600 hover:bg-army-700 text-white font-medium py-3 px-8 rounded-lg transition-colors"
        >
          Shop All Products
        </Link>
      </div>
    </div>
  );
}
