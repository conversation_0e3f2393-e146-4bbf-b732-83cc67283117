import {
  redirect,
  type LoaderFunctionArgs,
  type ActionFunctionArgs,
  type MetaFunction,
} from '@shopify/remix-oxygen';
import {useLoaderData, Link} from 'react-router';
import {
  Image,
  Money,
  getSelectedProductOptions,
  Analytics,
  useOptimisticVariant,
  getAdjacentAndFirstAvailableVariants,
  useSelectedOptionInUrlParam,
  getProductOptions,
} from '@shopify/hydrogen';
import {useState} from 'react';
import {AddToCartButton} from '~/components/AddToCartButton';
import {useAside} from '~/components/Aside';
import {SubscriptionDropdown} from '~/components/SubscriptionDropdown';

export const meta: MetaFunction<typeof loader> = ({data}) => {
  return [{title: `${data?.product?.title ?? ''} | Big River Coffee`}];
};

export async function loader({params, request, context}: LoaderFunctionArgs) {
  const {storefront} = context;

  const searchParams = new URL(request.url).searchParams;
  const selectedOptions = getSelectedProductOptions(request).filter(
    (option) =>
      // Filter out Shopify predictive search query params
      !option.name.startsWith('_sid') &&
      !option.name.startsWith('_pos') &&
      !option.name.startsWith('_psq') &&
      !option.name.startsWith('_ss') &&
      !option.name.startsWith('_v') &&
      // Filter out pagination query params
      !option.name.startsWith('cursor'),
  );

  // Query the specific product by ID
  const {product} = await storefront.query(ROASTERS_BOX_PRODUCT_QUERY, {
    variables: {
      id: 'gid://shopify/Product/10111587647803',
      selectedOptions,
    },
  });

  if (!product?.id) {
    throw new Response(null, {status: 404});
  }

  return {
    product,
  };
}

export default function RoastersBoxProduct() {
  const {product} = useLoaderData<typeof loader>();
  const {open} = useAside();

  // Optimistically selects a variant with given available variant information
  const selectedVariant = useOptimisticVariant(
    product.selectedOrFirstAvailableVariant,
    getAdjacentAndFirstAvailableVariants(product),
  );

  // Sets the search param to the selected variant without navigation
  // only when no search params are set in the url
  useSelectedOptionInUrlParam(selectedVariant.selectedOptions);

  // Get the product options array
  const productOptions = getProductOptions({
    ...product,
    selectedOrFirstAvailableVariant: selectedVariant,
  });

  const {title, descriptionHtml} = product;

  // State for quantity
  const [quantity, setQuantity] = useState(1);

  return (
    <div className="product">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            {product.featuredImage && (
              <div className="aspect-square bg-gray-100 rounded-xl overflow-hidden">
                <Image
                  alt={product.featuredImage.altText || product.title}
                  data={product.featuredImage}
                  className="w-full h-full object-cover"
                  sizes="(min-width: 1024px) 50vw, 100vw"
                />
              </div>
            )}
            
            {/* Additional Images */}
            {product.images?.nodes && product.images.nodes.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {product.images.nodes.slice(1, 5).map((image: any, index: number) => (
                  <div key={image.id} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      alt={image.altText || `${product.title} ${index + 2}`}
                      data={image}
                      className="w-full h-full object-cover"
                      sizes="(min-width: 1024px) 12vw, 25vw"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
              {selectedVariant && (
                <div className="text-2xl font-semibold text-army-600">
                  <Money data={selectedVariant.price} />
                </div>
              )}
            </div>

            {/* Product Description */}
            {descriptionHtml && (
              <div 
                className="prose prose-sm text-gray-600"
                dangerouslySetInnerHTML={{__html: descriptionHtml}}
              />
            )}

            {/* Product Options */}
            <div className="space-y-4">
              {productOptions.map((option) => (
                <div key={option.name}>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">{option.name}</h3>
                  <div className="flex flex-wrap gap-2">
                    {option.optionValues.map((value) => {
                      const isSelected = value.selected;
                      const isAvailable = value.available;
                      
                      return (
                        <Link
                          key={value.name}
                          to={(value as any).to || '#'}
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                            isSelected
                              ? 'bg-army-600 text-white shadow-sm'
                              : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                          } ${!isAvailable ? 'opacity-40 cursor-not-allowed' : ''}`}
                        >
                          {value.name}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Quantity */}
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-900">Quantity:</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
                <span className="w-12 text-center font-medium">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Subscription Dropdown */}
            {selectedVariant && (
              <SubscriptionDropdown
                product={product}
                selectedVariant={selectedVariant}
                quantity={quantity}
              />
            )}

            {/* Back to Collections */}
            <div className="pt-6 border-t border-gray-200">
              <Link
                to="/collections/all"
                className="inline-flex items-center text-army-600 hover:text-army-700 font-medium"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                See all coffee
              </Link>
            </div>
          </div>
        </div>

        {/* Adventure Promise Section */}
        <div className="mt-16 bg-army-600 rounded-xl overflow-hidden">
          <div className="px-8 py-12 md:px-12 md:py-16">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                The Adventure Promise
              </h2>
              <div className="w-20 h-1 bg-orange-500 rounded mb-8 mx-auto"></div>
              <p className="text-gray-100 text-lg leading-relaxed mb-8">
                Every cup of Big River Coffee is a gateway to adventure. We source our beans from the most remote and pristine coffee-growing regions, ensuring that each sip delivers not just exceptional flavor, but a story of exploration and discovery.
              </p>
              <p className="text-gray-100 text-lg leading-relaxed">
                From the misty mountains of Ethiopia to the volcanic soils of Guatemala, our coffee connects you to the wild places where the best beans grow. It's more than coffee—it's fuel for your next adventure.
              </p>
            </div>
          </div>
        </div>
      </div>

      <Analytics.ProductView
        data={{
          products: [
            {
              id: product.id,
              title: product.title,
              price: selectedVariant?.price?.amount || '0',
              vendor: product.vendor,
              variantId: selectedVariant?.id || '',
              variantTitle: selectedVariant?.title || '',
              quantity,
            },
          ],
        }}
      />
    </div>
  );
}

const ROASTERS_BOX_PRODUCT_VARIANT_FRAGMENT = `#graphql
  fragment RoastersBoxProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

const ROASTERS_BOX_PRODUCT_FRAGMENT = `#graphql
  fragment RoastersBoxProduct on Product {
    id
    title
    vendor
    handle
    descriptionHtml
    description
    encodedVariantExistence
    encodedVariantAvailability
    requiresSellingPlan
    sellingPlanGroups(first: 10) {
      nodes {
        name
        options {
          name
          values
        }
        sellingPlans(first: 10) {
          nodes {
            id
            name
            description
            options {
              name
              value
            }
            priceAdjustments {
              orderCount
              adjustmentValue {
                ... on SellingPlanFixedAmountPriceAdjustment {
                  adjustmentAmount {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanPercentagePriceAdjustment {
                  adjustmentPercentage
                }
              }
            }
            recurringDeliveries
            checkoutCharge {
              type
              value {
                ... on SellingPlanCheckoutChargePercentageValue {
                  percentage
                }
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
              }
            }
          }
        }
      }
    }
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...RoastersBoxProductVariant
        }
        swatch {
          color
          image {
            previewImage {
              url
            }
          }
        }
      }
    }
    featuredImage {
      id
      altText
      url
      width
      height
    }
    images(first: 20) {
      nodes {
        id
        altText
        url
        width
        height
      }
    }
    selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
      ...RoastersBoxProductVariant
    }
    adjacentVariants (selectedOptions: $selectedOptions) {
      ...RoastersBoxProductVariant
    }
    variants(first: 50) {
      nodes {
        ...RoastersBoxProductVariant
      }
    }
    seo {
      description
      title
    }
  }
  ${ROASTERS_BOX_PRODUCT_VARIANT_FRAGMENT}
` as const;

const ROASTERS_BOX_PRODUCT_QUERY = `#graphql
  query RoastersBoxProductById(
    $country: CountryCode
    $id: ID!
    $language: LanguageCode
    $selectedOptions: [SelectedOptionInput!]!
  ) @inContext(country: $country, language: $language) {
    product(id: $id) {
      ...RoastersBoxProduct
    }
  }
  ${ROASTERS_BOX_PRODUCT_FRAGMENT}
` as const;
