import{r as o}from"./chunk-D4RADZKF-DNKU_WG6.js";function t(){return o.useEffect(()=>{if(typeof window>"u"||window.dataLayer)return;window.dataLayer=window.dataLayer||[],window.dataLayer.push({"gtm.start":new Date().getTime(),event:"gtm.js"});const e=document.createElement("script");return e.async=!0,e.src="https://www.googletagmanager.com/gtm.js?id=GTM-WXN2JD85",e.onload=()=>{console.log("Google Tag Manager loaded")},e.onerror=()=>{console.error("Failed to load Google Tag Manager")},document.head.appendChild(e),()=>{e.parentNode&&e.parentNode.removeChild(e)}},[]),null}export{t as GTMLoader};
