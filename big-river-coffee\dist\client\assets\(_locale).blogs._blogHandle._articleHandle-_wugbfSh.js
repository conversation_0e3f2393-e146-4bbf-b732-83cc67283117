import{w as o}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{u as m}from"./chunk-D4RADZKF-DNKU_WG6.js";import{I as c}from"./Image-BhFgnyoc.js";const x=({data:t})=>[{title:`Hydrogen | ${(t==null?void 0:t.article.title)??""} article`}],g=o(function(){const{article:r}=m(),{title:n,image:i,contentHtml:l,author:s}=r,a=new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(new Date(r.publishedAt));return e.jsxs("div",{className:"article",children:[e.jsxs("h1",{children:[n,e.jsxs("div",{children:[e.jsx("time",{dateTime:r.publishedAt,children:a})," ·"," ",e.jsx("address",{children:s==null?void 0:s.name})]})]}),i&&e.jsx(c,{data:i,sizes:"90vw",loading:"eager"}),e.jsx("div",{dangerouslySetInnerHTML:{__html:l},className:"article"})]})});export{g as default,x as meta};
