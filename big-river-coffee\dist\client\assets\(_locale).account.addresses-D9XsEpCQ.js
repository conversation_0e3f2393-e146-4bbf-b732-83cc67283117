import{w as p}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{j as h,a as x,e as j,F as b}from"./chunk-D4RADZKF-DNKU_WG6.js";const A=()=>[{title:"Addresses"}],F=p(function(){const{customer:t}=h(),{defaultAddress:i,addresses:n}=t;return e.jsxs("div",{className:"account-addresses",children:[e.jsx("h2",{children:"Addresses"}),e.jsx("br",{}),n.nodes.length?e.jsxs("div",{children:[e.jsxs("div",{children:[e.jsx("legend",{children:"Create address"}),e.jsx(y,{})]}),e.jsx("br",{}),e.jsx("hr",{}),e.jsx("br",{}),e.jsx(C,{addresses:n,defaultAddress:i})]}):e.jsx("p",{children:"You have no addresses saved."})]})});function y(){const l={address1:"",address2:"",city:"",company:"",territoryCode:"",firstName:"",id:"new",lastName:"",phoneNumber:"",zoneCode:"",zip:""};return e.jsx(d,{addressId:"NEW_ADDRESS_ID",address:l,defaultAddress:null,children:({stateForMethod:t})=>e.jsx("div",{children:e.jsx("button",{disabled:t("POST")!=="idle",formMethod:"POST",type:"submit",children:t("POST")!=="idle"?"Creating":"Create"})})})}function C({addresses:l,defaultAddress:t}){return e.jsxs("div",{children:[e.jsx("legend",{children:"Existing addresses"}),l.nodes.map(i=>e.jsx(d,{addressId:i.id,address:i,defaultAddress:t,children:({stateForMethod:n})=>e.jsxs("div",{children:[e.jsx("button",{disabled:n("PUT")!=="idle",formMethod:"PUT",type:"submit",children:n("PUT")!=="idle"?"Saving":"Save"}),e.jsx("button",{disabled:n("DELETE")!=="idle",formMethod:"DELETE",type:"submit",children:n("DELETE")!=="idle"?"Deleting":"Delete"})]})},i.id))]})}function d({addressId:l,address:t,defaultAddress:i,children:n}){var o;const{state:u,formMethod:s}=x(),r=j(),a=(o=r==null?void 0:r.error)==null?void 0:o[l],m=(i==null?void 0:i.id)===l;return e.jsx(b,{id:l,children:e.jsxs("fieldset",{children:[e.jsx("input",{type:"hidden",name:"addressId",defaultValue:l}),e.jsx("label",{htmlFor:"firstName",children:"First name*"}),e.jsx("input",{"aria-label":"First name",autoComplete:"given-name",defaultValue:(t==null?void 0:t.firstName)??"",id:"firstName",name:"firstName",placeholder:"First name",required:!0,type:"text"}),e.jsx("label",{htmlFor:"lastName",children:"Last name*"}),e.jsx("input",{"aria-label":"Last name",autoComplete:"family-name",defaultValue:(t==null?void 0:t.lastName)??"",id:"lastName",name:"lastName",placeholder:"Last name",required:!0,type:"text"}),e.jsx("label",{htmlFor:"company",children:"Company"}),e.jsx("input",{"aria-label":"Company",autoComplete:"organization",defaultValue:(t==null?void 0:t.company)??"",id:"company",name:"company",placeholder:"Company",type:"text"}),e.jsx("label",{htmlFor:"address1",children:"Address line*"}),e.jsx("input",{"aria-label":"Address line 1",autoComplete:"address-line1",defaultValue:(t==null?void 0:t.address1)??"",id:"address1",name:"address1",placeholder:"Address line 1*",required:!0,type:"text"}),e.jsx("label",{htmlFor:"address2",children:"Address line 2"}),e.jsx("input",{"aria-label":"Address line 2",autoComplete:"address-line2",defaultValue:(t==null?void 0:t.address2)??"",id:"address2",name:"address2",placeholder:"Address line 2",type:"text"}),e.jsx("label",{htmlFor:"city",children:"City*"}),e.jsx("input",{"aria-label":"City",autoComplete:"address-level2",defaultValue:(t==null?void 0:t.city)??"",id:"city",name:"city",placeholder:"City",required:!0,type:"text"}),e.jsx("label",{htmlFor:"zoneCode",children:"State / Province*"}),e.jsx("input",{"aria-label":"State/Province",autoComplete:"address-level1",defaultValue:(t==null?void 0:t.zoneCode)??"",id:"zoneCode",name:"zoneCode",placeholder:"State / Province",required:!0,type:"text"}),e.jsx("label",{htmlFor:"zip",children:"Zip / Postal Code*"}),e.jsx("input",{"aria-label":"Zip",autoComplete:"postal-code",defaultValue:(t==null?void 0:t.zip)??"",id:"zip",name:"zip",placeholder:"Zip / Postal Code",required:!0,type:"text"}),e.jsx("label",{htmlFor:"territoryCode",children:"Country Code*"}),e.jsx("input",{"aria-label":"territoryCode",autoComplete:"country",defaultValue:(t==null?void 0:t.territoryCode)??"",id:"territoryCode",name:"territoryCode",placeholder:"Country",required:!0,type:"text",maxLength:2}),e.jsx("label",{htmlFor:"phoneNumber",children:"Phone"}),e.jsx("input",{"aria-label":"Phone Number",autoComplete:"tel",defaultValue:(t==null?void 0:t.phoneNumber)??"",id:"phoneNumber",name:"phoneNumber",placeholder:"+16135551111",pattern:"^\\+?[1-9]\\d{3,14}$",type:"tel"}),e.jsxs("div",{children:[e.jsx("input",{defaultChecked:m,id:"defaultAddress",name:"defaultAddress",type:"checkbox"}),e.jsx("label",{htmlFor:"defaultAddress",children:"Set as default address"})]}),a?e.jsx("p",{children:e.jsx("mark",{children:e.jsx("small",{children:a})})}):e.jsx("br",{}),n({stateForMethod:c=>s===c?u:"idle"})]})})}export{F as default,A as meta};
