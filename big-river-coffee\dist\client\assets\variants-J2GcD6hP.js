import{i as s,r as e}from"./chunk-D4RADZKF-DNKU_WG6.js";function f(r,t){const{pathname:a}=s();return e.useMemo(()=>g({handle:r,pathname:a,searchParams:new URLSearchParams,selectedOptions:t}),[r,t,a])}function g({handle:r,pathname:t,searchParams:a,selectedOptions:n}){const o=/(\/[a-zA-Z]{2}-[a-zA-Z]{2}\/)/g.exec(t),u=o&&o.length>0?`${o[0]}products/${r}`:`/products/${r}`;n==null||n.forEach(c=>{a.set(c.name,c.value)});const m=a.toString();return u+(m?"?"+a.toString():"")}export{f as u};
