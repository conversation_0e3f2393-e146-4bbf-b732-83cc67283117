import {Link, useNavigate} from 'react-router';
import {type MappedProductOptions} from '@shopify/hydrogen';
import type {
  Maybe,
  ProductOptionValueSwatch,
} from '@shopify/hydrogen/storefront-api-types';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment} from 'storefrontapi.generated';
import {useState} from 'react';

export function ProductForm({
  productOptions,
  selectedVariant,
  product,
}: {
  productOptions: MappedProductOptions[];
  selectedVariant: ProductFragment['selectedOrFirstAvailableVariant'];
  product?: ProductFragment;
}) {
  const navigate = useNavigate();
  const {open} = useAside();
  const [quantity, setQuantity] = useState(1);
  const [selectedSellingPlanId, setSelectedSellingPlanId] = useState<string | null>(null);
  const [selectedPurchaseType, setSelectedPurchaseType] = useState<'onetime' | 'subscription'>('onetime');

  return (
    <div className="product-form space-y-6">
      {/* Product Options */}
      {productOptions.map((option) => {
        // If there is only a single value in the option values, don't display the option
        if (option.optionValues.length === 1) return null;

        return (
          <div className="space-y-3" key={option.name}>
            <h3 className="text-sm font-medium !text-black">{option.name}</h3>
            <div className="flex flex-wrap gap-2">
              {option.optionValues.map((value) => {
                const {
                  name,
                  handle,
                  variantUriQuery,
                  selected,
                  available,
                  exists,
                  isDifferentProduct,
                  swatch,
                } = value;

                // Common class for all variant buttons/links
                const baseClasses = `px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  selected
                    ? 'bg-army-600 text-white shadow-sm'
                    : 'bg-white !text-black hover:bg-gray-200 border border-gray-300'
                } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : ''}`;

                if (isDifferentProduct) {
                  // SEO
                  // When the variant is a combined listing child product
                  // that leads to a different url, we need to render it
                  // as an anchor tag
                  return (
                    <Link
                      className={baseClasses}
                      key={option.name + name}
                      prefetch="intent"
                      preventScrollReset
                      replace
                      to={`/products/${handle}?${variantUriQuery}`}
                    >
                      {swatch?.color || swatch?.image ? (
                        <ProductOptionSwatch swatch={swatch} name={name} />
                      ) : (
                        name
                      )}
                    </Link>
                  );
                } else {
                  // SEO
                  // When the variant is an update to the search param,
                  // render it as a button with javascript navigating to
                  // the variant so that SEO bots do not index these as
                  // duplicated links
                  return (
                    <button
                      type="button"
                      className={baseClasses}
                      key={option.name + name}
                      disabled={!exists}
                      onClick={() => {
                        if (!selected) {
                          navigate(`?${variantUriQuery}`, {
                            replace: true,
                            preventScrollReset: true,
                          });
                        }
                      }}
                    >
                      {swatch?.color || swatch?.image ? (
                        <ProductOptionSwatch swatch={swatch} name={name} />
                      ) : (
                        name
                      )}
                    </button>
                  );
                }
              })}
            </div>
          </div>
        );
      })}

      {/* Quantity Selector */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium !text-black">Quantity</h3>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            className="w-10 h-10 rounded-md border border-gray-300 bg-white flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={quantity <= 1}
          >
            <svg className="w-4 h-4 !text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          <span className="w-12 text-center font-medium !text-black bg-white px-2 py-1 rounded">{quantity}</span>
          <button
            type="button"
            onClick={() => setQuantity(quantity + 1)}
            className="w-10 h-10 rounded-md border border-gray-300 bg-white flex items-center justify-center hover:bg-gray-50 transition-colors"
          >
            <svg className="w-4 h-4 !text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
      </div>

      {/* Sales-Optimized Purchase Options */}
      {product && selectedVariant && (
        <SalesOptimizedPurchaseOptions
          product={product}
          selectedVariant={selectedVariant}
          quantity={quantity}
          selectedPurchaseType={selectedPurchaseType}
          setSelectedPurchaseType={setSelectedPurchaseType}
          selectedSellingPlanId={selectedSellingPlanId}
          setSelectedSellingPlanId={setSelectedSellingPlanId}
        />
      )}
    </div>
  );
}

function SalesOptimizedPurchaseOptions({
  product,
  selectedVariant,
  quantity,
  selectedPurchaseType,
  setSelectedPurchaseType,
  selectedSellingPlanId,
  setSelectedSellingPlanId,
}: {
  product: ProductFragment;
  selectedVariant: ProductFragment['selectedOrFirstAvailableVariant'];
  quantity: number;
  selectedPurchaseType: 'onetime' | 'subscription';
  setSelectedPurchaseType: (type: 'onetime' | 'subscription') => void;
  selectedSellingPlanId: string | null;
  setSelectedSellingPlanId: (id: string | null) => void;
}) {
  const {open} = useAside();

  // Subscription options
  const subscriptionOptions = [
    {
      id: 'weekly',
      name: 'Weekly',
      description: 'Delivered every week',
      sellingPlanId: 'gid://shopify/SellingPlan/9581953339',
      discount: '15%'
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: 'Delivered every month',
      sellingPlanId: 'gid://shopify/SellingPlan/9581986107',
      discount: '15%'
    },
    {
      id: 'every3weeks',
      name: 'Every 3 weeks',
      description: 'Delivered every 3 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582018875',
      discount: '15%'
    },
    {
      id: 'every6weeks',
      name: 'Every 6 weeks',
      description: 'Delivered every 6 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582051643',
      discount: '15%'
    }
  ];

  // Check if product has selling plans
  const hasSellingPlans = product?.sellingPlanGroups?.nodes && product.sellingPlanGroups.nodes.length > 0;

  // Calculate savings for quantity purchases
  const calculateSavings = (qty: number) => {
    if (qty >= 3) return 10;
    if (qty >= 2) return 5;
    return 0;
  };

  const basePrice = selectedVariant?.price ? parseFloat(selectedVariant.price.amount) : 0;

  return (
    <div className="space-y-6">
      {/* Enhanced Purchase Type Selection */}
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-2xl font-bold !text-black mb-2">How would you like to purchase?</h3>
          <p className="text-gray-600">Choose the option that works best for you</p>
        </div>

        {/* One-time vs Subscription Toggle */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <button
            type="button"
            onClick={() => setSelectedPurchaseType('onetime')}
            className={`p-6 rounded-xl border-2 transition-all duration-300 transform hover:scale-[1.02] ${
              selectedPurchaseType === 'onetime'
                ? 'border-army-600 bg-army-50 text-army-700 shadow-lg'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:shadow-md'
            }`}
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-army-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="font-bold text-lg">One-time Purchase</div>
              <div className="text-sm opacity-75 mt-1">Perfect for trying new flavors</div>
              <div className="text-xs mt-2 text-gray-500">• No commitment • Full price</div>
            </div>
          </button>

          {hasSellingPlans && (
            <button
              type="button"
              onClick={() => setSelectedPurchaseType('subscription')}
              className={`p-6 rounded-xl border-2 transition-all duration-300 transform hover:scale-[1.02] relative overflow-hidden ${
                selectedPurchaseType === 'subscription'
                  ? 'border-[#db8027] bg-gradient-to-br from-orange-50 to-yellow-50 text-[#db8027] shadow-lg'
                  : 'border-gray-200 bg-white text-gray-700 hover:border-[#db8027] hover:shadow-md'
              }`}
            >
              {/* Animated background for subscription */}
              <div className="absolute inset-0 bg-gradient-to-r from-[#db8027]/5 via-[#db8027]/10 to-[#db8027]/5 transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>

              <div className="relative text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-[#db8027] to-[#c4721f] rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="font-bold text-lg">Subscribe & Save</div>
                <div className="text-sm opacity-75 mt-1">Never run out of great coffee</div>
                <div className="text-xs mt-2">
                  <span className="text-green-600 font-semibold">• Save 15% every order</span><br/>
                  <span className="text-gray-500">• Cancel anytime • Free shipping</span>
                </div>
              </div>

              {/* Floating discount badge */}
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg animate-bounce">
                15% OFF
              </div>

              {/* Popular choice ribbon */}
              <div className="absolute top-0 left-0 bg-[#db8027] text-white text-xs px-2 py-1 rounded-br-lg font-bold">
                POPULAR
              </div>
            </button>
          )}
        </div>

        {/* Enhanced Subscription Frequency */}
        {selectedPurchaseType === 'subscription' && hasSellingPlans && (
          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-6 border-2 border-[#db8027]/30">
            <div className="text-center mb-4">
              <h4 className="text-lg font-bold text-[#db8027] mb-2">🚚 Choose Your Delivery Schedule</h4>
              <p className="text-sm text-gray-600">We'll deliver fresh coffee right to your door</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {subscriptionOptions.map((option) => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => setSelectedSellingPlanId(option.sellingPlanId)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                    (selectedSellingPlanId || subscriptionOptions[1].sellingPlanId) === option.sellingPlanId
                      ? 'border-[#db8027] bg-white text-[#db8027] shadow-md'
                      : 'border-gray-200 bg-white/50 text-gray-700 hover:border-[#db8027]/50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold">{option.name}</div>
                      <div className="text-sm opacity-75">{option.description}</div>
                    </div>
                    <div className="flex items-center">
                      {(selectedSellingPlanId || subscriptionOptions[1].sellingPlanId) === option.sellingPlanId && (
                        <svg className="w-5 h-5 text-[#db8027]" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-4 text-center">
              <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Free shipping on all subscription orders</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Quantity-Based Savings Buttons */}
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-2xl font-bold !text-black mb-2">Choose Your Quantity</h3>
          <p className="text-gray-600">Buy more and save more on every order!</p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          {/* Single Item - Standard Option */}
          <div className="relative group">
            <AddToCartButton
              disabled={!selectedVariant || !selectedVariant.availableForSale}
              onClick={() => open('cart')}
              lines={
                selectedVariant
                  ? [
                      {
                        merchandiseId: selectedVariant.id,
                        quantity: 1,
                        selectedVariant,
                        ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                          sellingPlanId: selectedSellingPlanId,
                        }),
                      },
                    ]
                  : []
              }
              className="w-full bg-gradient-to-r from-army-600 to-army-700 hover:from-army-700 hover:to-army-800 font-semibold py-6 px-8 rounded-xl transition-all duration-300 text-white text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] border-2 border-transparent"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-white/20 rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6V5a2 2 0 114 0v1H8zm2 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-xl">Buy 1</div>
                    <div className="text-sm opacity-90">Standard Price</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    ${basePrice.toFixed(2)}
                  </div>
                  <div className="text-sm opacity-90">per bag</div>
                </div>
              </div>
            </AddToCartButton>
          </div>

          {/* 2 Items with 5% savings - Popular Choice */}
          <div className="relative group">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
              <div className="bg-green-500 text-white text-sm font-bold px-4 py-1 rounded-full shadow-lg">
                💰 SAVE 5%
              </div>
            </div>
            <AddToCartButton
              disabled={!selectedVariant || !selectedVariant.availableForSale}
              onClick={() => open('cart')}
              lines={
                selectedVariant
                  ? [
                      {
                        merchandiseId: selectedVariant.id,
                        quantity: 2,
                        selectedVariant,
                        ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                          sellingPlanId: selectedSellingPlanId,
                        }),
                      },
                    ]
                  : []
              }
              className="w-full bg-gradient-to-r from-[#5d8e8e] to-[#4a7373] hover:from-[#4a7373] hover:to-[#3d5f5f] font-semibold py-6 px-8 rounded-xl transition-all duration-300 text-white text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] border-2 border-green-400"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-white/20 rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6V5a2 2 0 114 0v1H8zm2 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-xl">Buy 2</div>
                    <div className="text-sm opacity-90">Popular Choice</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    ${(basePrice * 0.95).toFixed(2)}
                  </div>
                  <div className="text-sm opacity-90">per bag</div>
                  <div className="text-xs bg-white/20 rounded px-2 py-1 mt-1">
                    Save ${(basePrice * 0.05 * 2).toFixed(2)}
                  </div>
                </div>
              </div>
            </AddToCartButton>
          </div>

          {/* 3 Items with 10% savings - Best Value */}
          <div className="relative group">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
              <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white text-sm font-bold px-4 py-1 rounded-full shadow-lg animate-pulse">
                🔥 BEST VALUE - SAVE 10%
              </div>
            </div>
            <AddToCartButton
              disabled={!selectedVariant || !selectedVariant.availableForSale}
              onClick={() => open('cart')}
              lines={
                selectedVariant
                  ? [
                      {
                        merchandiseId: selectedVariant.id,
                        quantity: 3,
                        selectedVariant,
                        ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                          sellingPlanId: selectedSellingPlanId,
                        }),
                      },
                    ]
                  : []
              }
              className="w-full bg-gradient-to-r from-[#db8027] to-[#c4721f] hover:from-[#c4721f] hover:to-[#b25538] font-semibold py-6 px-8 rounded-xl transition-all duration-300 text-white text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] border-2 border-[#db8027] relative overflow-hidden"
            >
              {/* Animated background effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-white/20 rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6V5a2 2 0 114 0v1H8zm2 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-xl">Buy 3</div>
                    <div className="text-sm opacity-90">Best Value!</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    ${(basePrice * 0.90).toFixed(2)}
                  </div>
                  <div className="text-sm opacity-90">per bag</div>
                  <div className="text-xs bg-white/20 rounded px-2 py-1 mt-1">
                    Save ${(basePrice * 0.10 * 3).toFixed(2)}
                  </div>
                </div>
              </div>
            </AddToCartButton>
          </div>
        </div>

        {/* Subscription Bonus Message */}
        {selectedPurchaseType === 'subscription' && (
          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border-2 border-[#db8027] rounded-xl p-4 text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <svg className="w-6 h-6 text-[#db8027]" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
              <span className="text-lg font-bold text-[#db8027]">SUBSCRIPTION BONUS!</span>
            </div>
            <p className="text-[#db8027] font-medium">
              Stack your quantity savings with 15% subscription discount for maximum value!
            </p>
          </div>
        )}

        {/* Enhanced Savings Calculator */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border-2 border-gray-200 shadow-inner">
          <div className="text-center mb-6">
            <h4 className="text-xl font-bold text-gray-800 mb-2">💰 Smart Savings Calculator</h4>
            <p className="text-gray-600">See how much you save with each option</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
              <div className="text-center">
                <div className="text-3xl font-bold text-army-600 mb-1">1x</div>
                <div className="text-sm text-gray-600 mb-2">Standard Price</div>
                <div className="text-xl font-bold text-gray-800">${basePrice.toFixed(2)}</div>
                <div className="text-sm text-gray-500 mt-1">per bag</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 shadow-sm border-2 border-green-300 relative">
              <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                SAVE
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">2x</div>
                <div className="text-sm text-green-600 mb-2 font-medium">5% Off Each</div>
                <div className="text-xl font-bold text-green-700">${(basePrice * 0.95).toFixed(2)}</div>
                <div className="text-sm text-green-600 mt-1">per bag</div>
                <div className="text-xs bg-green-200 text-green-800 rounded px-2 py-1 mt-2 font-bold">
                  Save ${(basePrice * 0.05 * 2).toFixed(2)} total
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-yellow-100 rounded-lg p-4 shadow-sm border-2 border-[#db8027] relative overflow-hidden">
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white text-xs px-2 py-1 rounded-full font-bold animate-pulse">
                BEST
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 animate-pulse"></div>
              <div className="relative text-center">
                <div className="text-3xl font-bold text-[#db8027] mb-1">3x</div>
                <div className="text-sm text-[#db8027] mb-2 font-medium">10% Off Each</div>
                <div className="text-xl font-bold text-[#db8027]">${(basePrice * 0.90).toFixed(2)}</div>
                <div className="text-sm text-[#db8027] mt-1">per bag</div>
                <div className="text-xs bg-[#db8027] text-white rounded px-2 py-1 mt-2 font-bold">
                  Save ${(basePrice * 0.10 * 3).toFixed(2)} total
                </div>
              </div>
            </div>
          </div>

          {/* Maximum Savings Potential */}
          <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-4 border-2 border-purple-300">
            <div className="text-center">
              <h5 className="text-lg font-bold text-purple-800 mb-2">🚀 Maximum Savings Potential</h5>
              <div className="text-sm text-purple-700 mb-3">
                Combine quantity discount + subscription savings for ultimate value!
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
                <div className="bg-white/50 rounded p-2">
                  <div className="font-semibold text-purple-800">Buy 3 + Subscribe</div>
                  <div className="text-purple-600">10% + 15% = 25% OFF</div>
                  <div className="font-bold text-purple-800">${(basePrice * 0.75).toFixed(2)} per bag</div>
                </div>
                <div className="bg-white/50 rounded p-2">
                  <div className="font-semibold text-purple-800">Annual Savings</div>
                  <div className="text-purple-600">Monthly delivery</div>
                  <div className="font-bold text-purple-800">${((basePrice * 0.25) * 12 * 3).toFixed(0)}/year</div>
                </div>
                <div className="bg-white/50 rounded p-2">
                  <div className="font-semibold text-purple-800">Free Shipping</div>
                  <div className="text-purple-600">All subscriptions</div>
                  <div className="font-bold text-purple-800">$0 shipping</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProductOptionSwatch({
  swatch,
  name,
}: {
  swatch?: Maybe<ProductOptionValueSwatch> | undefined;
  name: string;
}) {
  const image = swatch?.image?.previewImage?.url;
  const color = swatch?.color;

  if (!image && !color) return name;

  return (
    <div
      aria-label={name}
      className="product-option-label-swatch"
      style={{
        backgroundColor: color || 'transparent',
      }}
    >
      {!!image && <img src={image} alt={name} />}
    </div>
  );
}
