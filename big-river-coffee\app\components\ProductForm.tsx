import {Link, useNavigate} from 'react-router';
import {type MappedProductOptions} from '@shopify/hydrogen';
import type {
  Maybe,
  ProductOptionValueSwatch,
} from '@shopify/hydrogen/storefront-api-types';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment} from 'storefrontapi.generated';
import {useState} from 'react';

export function ProductForm({
  productOptions,
  selectedVariant,
  product,
}: {
  productOptions: MappedProductOptions[];
  selectedVariant: ProductFragment['selectedOrFirstAvailableVariant'];
  product?: ProductFragment;
}) {
  const navigate = useNavigate();
  const {open} = useAside();
  const [quantity, setQuantity] = useState(1);
  const [selectedSellingPlanId, setSelectedSellingPlanId] = useState<string | null>(null);
  const [selectedPurchaseType, setSelectedPurchaseType] = useState<'onetime' | 'subscription'>('onetime');

  return (
    <div className="product-form space-y-6">
      {/* Product Options */}
      {productOptions.map((option) => {
        // If there is only a single value in the option values, don't display the option
        if (option.optionValues.length === 1) return null;

        return (
          <div className="space-y-3" key={option.name}>
            <h3 className="text-sm font-medium !text-black">{option.name}</h3>
            <div className="flex flex-wrap gap-2">
              {option.optionValues.map((value) => {
                const {
                  name,
                  handle,
                  variantUriQuery,
                  selected,
                  available,
                  exists,
                  isDifferentProduct,
                  swatch,
                } = value;

                // Common class for all variant buttons/links
                const baseClasses = `px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  selected
                    ? 'bg-army-600 text-white shadow-sm'
                    : 'bg-white !text-black hover:bg-gray-200 border border-gray-300'
                } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : ''}`;

                if (isDifferentProduct) {
                  // SEO
                  // When the variant is a combined listing child product
                  // that leads to a different url, we need to render it
                  // as an anchor tag
                  return (
                    <Link
                      className={baseClasses}
                      key={option.name + name}
                      prefetch="intent"
                      preventScrollReset
                      replace
                      to={`/products/${handle}?${variantUriQuery}`}
                    >
                      {swatch?.color || swatch?.image ? (
                        <ProductOptionSwatch swatch={swatch} name={name} />
                      ) : (
                        name
                      )}
                    </Link>
                  );
                } else {
                  // SEO
                  // When the variant is an update to the search param,
                  // render it as a button with javascript navigating to
                  // the variant so that SEO bots do not index these as
                  // duplicated links
                  return (
                    <button
                      type="button"
                      className={baseClasses}
                      key={option.name + name}
                      disabled={!exists}
                      onClick={() => {
                        if (!selected) {
                          navigate(`?${variantUriQuery}`, {
                            replace: true,
                            preventScrollReset: true,
                          });
                        }
                      }}
                    >
                      {swatch?.color || swatch?.image ? (
                        <ProductOptionSwatch swatch={swatch} name={name} />
                      ) : (
                        name
                      )}
                    </button>
                  );
                }
              })}
            </div>
          </div>
        );
      })}

      {/* Quantity Selector */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium !text-black">Quantity</h3>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            className="w-10 h-10 rounded-md border border-gray-300 bg-white flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={quantity <= 1}
          >
            <svg className="w-4 h-4 !text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          <span className="w-12 text-center font-medium !text-black bg-white px-2 py-1 rounded">{quantity}</span>
          <button
            type="button"
            onClick={() => setQuantity(quantity + 1)}
            className="w-10 h-10 rounded-md border border-gray-300 bg-white flex items-center justify-center hover:bg-gray-50 transition-colors"
          >
            <svg className="w-4 h-4 !text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
      </div>

      {/* Sales-Optimized Purchase Options */}
      {product && selectedVariant && (
        <SalesOptimizedPurchaseOptions
          product={product}
          selectedVariant={selectedVariant}
          quantity={quantity}
          selectedPurchaseType={selectedPurchaseType}
          setSelectedPurchaseType={setSelectedPurchaseType}
          selectedSellingPlanId={selectedSellingPlanId}
          setSelectedSellingPlanId={setSelectedSellingPlanId}
        />
      )}
    </div>
  );
}

function SalesOptimizedPurchaseOptions({
  product,
  selectedVariant,
  quantity,
  selectedPurchaseType,
  setSelectedPurchaseType,
  selectedSellingPlanId,
  setSelectedSellingPlanId,
}: {
  product: ProductFragment;
  selectedVariant: ProductFragment['selectedOrFirstAvailableVariant'];
  quantity: number;
  selectedPurchaseType: 'onetime' | 'subscription';
  setSelectedPurchaseType: (type: 'onetime' | 'subscription') => void;
  selectedSellingPlanId: string | null;
  setSelectedSellingPlanId: (id: string | null) => void;
}) {
  const {open} = useAside();

  // Subscription options
  const subscriptionOptions = [
    {
      id: 'weekly',
      name: 'Weekly',
      description: 'Delivered every week',
      sellingPlanId: 'gid://shopify/SellingPlan/9581953339',
      discount: '15%'
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: 'Delivered every month',
      sellingPlanId: 'gid://shopify/SellingPlan/9581986107',
      discount: '15%'
    },
    {
      id: 'every3weeks',
      name: 'Every 3 weeks',
      description: 'Delivered every 3 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582018875',
      discount: '15%'
    },
    {
      id: 'every6weeks',
      name: 'Every 6 weeks',
      description: 'Delivered every 6 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582051643',
      discount: '15%'
    }
  ];

  // Check if product has selling plans
  const hasSellingPlans = product?.sellingPlanGroups?.nodes && product.sellingPlanGroups.nodes.length > 0;

  // Calculate savings for quantity purchases
  const calculateSavings = (qty: number) => {
    if (qty >= 3) return 10;
    if (qty >= 2) return 5;
    return 0;
  };

  const basePrice = selectedVariant?.price ? parseFloat(selectedVariant.price.amount) : 0;

  return (
    <div className="space-y-6">
      {/* Purchase Type Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold !text-black">Choose Your Purchase Option</h3>

        {/* One-time vs Subscription Toggle */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button
            type="button"
            onClick={() => setSelectedPurchaseType('onetime')}
            className={`p-4 rounded-lg border-2 transition-all duration-200 ${
              selectedPurchaseType === 'onetime'
                ? 'border-army-600 bg-army-50 text-army-700'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="text-left">
              <div className="font-medium">One-time Purchase</div>
              <div className="text-sm opacity-75">No commitment</div>
            </div>
          </button>

          {hasSellingPlans && (
            <button
              type="button"
              onClick={() => setSelectedPurchaseType('subscription')}
              className={`p-4 rounded-lg border-2 transition-all duration-200 relative ${
                selectedPurchaseType === 'subscription'
                  ? 'border-[#db8027] bg-orange-50 text-[#db8027]'
                  : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="text-left">
                <div className="font-medium">Subscribe & Save</div>
                <div className="text-sm opacity-75">Save 15% on every order</div>
              </div>
              <div className="absolute -top-2 -right-2 bg-[#db8027] text-white text-xs px-2 py-1 rounded-full font-bold">
                15% OFF
              </div>
            </button>
          )}
        </div>

        {/* Subscription Frequency (only show if subscription is selected) */}
        {selectedPurchaseType === 'subscription' && hasSellingPlans && (
          <div className="space-y-3">
            <label className="text-sm font-medium !text-black">Delivery Frequency</label>
            <select
              value={selectedSellingPlanId || subscriptionOptions[1].sellingPlanId}
              onChange={(e) => setSelectedSellingPlanId(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:ring-2 focus:ring-army-500 focus:border-army-500"
            >
              {subscriptionOptions.map((option) => (
                <option key={option.id} value={option.sellingPlanId}>
                  {option.name} - {option.description}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Quantity-Based Savings Buttons */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold !text-black">Add to Cart Options</h3>
        <div className="grid grid-cols-1 gap-3">
          {/* Single Item */}
          <AddToCartButton
            disabled={!selectedVariant || !selectedVariant.availableForSale}
            onClick={() => open('cart')}
            lines={
              selectedVariant
                ? [
                    {
                      merchandiseId: selectedVariant.id,
                      quantity: 1,
                      selectedVariant,
                      ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                        sellingPlanId: selectedSellingPlanId,
                      }),
                    },
                  ]
                : []
            }
            className="w-full bg-army-600 hover:bg-army-700 font-medium py-4 px-6 rounded-lg transition-colors duration-200 text-white text-lg"
          >
            {selectedVariant?.availableForSale ? 'Add 1 to Cart' : 'Sold out'}
          </AddToCartButton>

          {/* 2 Items with 5% savings */}
          <AddToCartButton
            disabled={!selectedVariant || !selectedVariant.availableForSale}
            onClick={() => open('cart')}
            lines={
              selectedVariant
                ? [
                    {
                      merchandiseId: selectedVariant.id,
                      quantity: 2,
                      selectedVariant,
                      ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                        sellingPlanId: selectedSellingPlanId,
                      }),
                    },
                  ]
                : []
            }
            className="w-full bg-[#5d8e8e] hover:bg-[#4a7373] font-medium py-4 px-6 rounded-lg transition-colors duration-200 text-white text-lg relative"
          >
            <div className="flex items-center justify-between">
              <span>{selectedVariant?.availableForSale ? 'Add 2 to Cart' : 'Sold out'}</span>
              <span className="bg-green-500 text-white text-sm px-2 py-1 rounded-full font-bold">
                Save 5%
              </span>
            </div>
          </AddToCartButton>

          {/* 3 Items with 10% savings */}
          <AddToCartButton
            disabled={!selectedVariant || !selectedVariant.availableForSale}
            onClick={() => open('cart')}
            lines={
              selectedVariant
                ? [
                    {
                      merchandiseId: selectedVariant.id,
                      quantity: 3,
                      selectedVariant,
                      ...(selectedPurchaseType === 'subscription' && selectedSellingPlanId && {
                        sellingPlanId: selectedSellingPlanId,
                      }),
                    },
                  ]
                : []
            }
            className="w-full bg-[#db8027] hover:bg-[#c4721f] font-medium py-4 px-6 rounded-lg transition-colors duration-200 text-white text-lg relative"
          >
            <div className="flex items-center justify-between">
              <span>{selectedVariant?.availableForSale ? 'Add 3 to Cart' : 'Sold out'}</span>
              <span className="bg-green-500 text-white text-sm px-2 py-1 rounded-full font-bold">
                Save 10%
              </span>
            </div>
          </AddToCartButton>
        </div>
      </div>
    </div>
  );
}

function ProductOptionSwatch({
  swatch,
  name,
}: {
  swatch?: Maybe<ProductOptionValueSwatch> | undefined;
  name: string;
}) {
  const image = swatch?.image?.previewImage?.url;
  const color = swatch?.color;

  if (!image && !color) return name;

  return (
    <div
      aria-label={name}
      className="product-option-label-swatch"
      style={{
        backgroundColor: color || 'transparent',
      }}
    >
      {!!image && <img src={image} alt={name} />}
    </div>
  );
}
