const v=",",d={OPTION:":",END_OF_PREFIX:",",SEQUENCE_GAP:" ",RANGE:"-"},j=(()=>{const t=new Map;return function(e,s){var n;if(e.length===0)return!1;if(!t.has(s)){const o=new Set;for(const a of F(s)){o.add(a.join(v));for(let i=0;i<a.length;i++)o.add(a.slice(0,i+1).join(v))}t.set(s,o)}return!!((n=t.get(s))!=null&&n.has(e.join(v)))}})();function F(t){if(!t)return[];if(t.startsWith("v1_"))return C(w(t));throw new Error("Unsupported option value encoding")}const w=t=>t.replace(/^v1_/,"");function C(t){const e=/[ :,-]/g;let s=0,n;const o=[],a=[];let i=0,r=null;for(;n=e.exec(t);){const l=n[0],f=Number.parseInt(t.slice(s,n.index))||0;if(r!==null){for(;r<f;r++)a[i]=r,o.push([...a]);r=null}a[i]=f,l===d.RANGE?r=f:l===d.OPTION?i++:((l===d.SEQUENCE_GAP||l===d.END_OF_PREFIX&&t[n.index-1]!==d.END_OF_PREFIX)&&o.push([...a]),l===d.END_OF_PREFIX&&(a.pop(),i--)),s=e.lastIndex}const p=t.match(/\d+$/g);if(p){const l=parseInt(p[0]);if(r!=null)for(;r<=l;r++)a[i]=r,o.push([...a]);else o.push([l])}return o}function $(t){return Object.assign({},...t.map(e=>({[e.name]:Object.assign({},...e!=null&&e.optionValues?e.optionValues.map((s,n)=>({[s.name]:n})):[])})))}function g(t){return Object.assign({},...t.map(e=>({[e.name]:e.value})))}function P(t){return JSON.stringify(g(t))}function N(t){return Array.isArray(t)?JSON.stringify(Object.assign({},...t.map(e=>({[e.name]:e.value})))):JSON.stringify(t)}function X(t,e){return Object.keys(t).map(n=>e[n]?e[n][t[n]]:null).filter(n=>n!==null)}function G(t){return Object.assign({},...t.map(e=>({[N(e.selectedOptions||[])]:e})))}const y=["options","selectedOrFirstAvailableVariant","adjacentVariants"],J=["handle","encodedVariantExistence","encodedVariantAvailability"];function u(t){return console.error(`[h2:error:getProductOptions] product.${t} is missing. Make sure you query for this field from the Storefront API.`),!1}function I(t,e=!1){var s;let n=!0;const o=Object.keys(t);if((e?[...y,...J]:y).forEach(a=>{o.includes(a)||(n=u(a))}),t.options){const a=t==null?void 0:t.options[0];if(e&&!(a!=null&&a.name)&&(n=u("options.name")),(s=t==null?void 0:t.options[0])!=null&&s.optionValues){let i=t.options[0].optionValues[0];e&&!(i!=null&&i.name)&&(n=u("options.optionValues.name")),i=t.options[0].optionValues.filter(r=>!!(r!=null&&r.firstSelectableVariant))[0],i!=null&&i.firstSelectableVariant&&(n=b(i.firstSelectableVariant,"options.optionValues.firstSelectableVariant",n,e))}else n=u("options.optionValues")}return t.selectedOrFirstAvailableVariant&&(n=b(t.selectedOrFirstAvailableVariant,"selectedOrFirstAvailableVariant",n,e)),t.adjacentVariants&&t.adjacentVariants[0]&&(n=b(t.adjacentVariants[0],"adjacentVariants",n,e)),n?t:{}}function b(t,e,s,n){var o;let a=s;if(n&&!((o=t.product)!=null&&o.handle)&&(a=u(`${e}.product.handle`)),t.selectedOptions){const i=t.selectedOptions[0];i!=null&&i.name||(a=u(`${e}.selectedOptions.name`)),i!=null&&i.value||(a=u(`${e}.selectedOptions.value`))}else a=u(`${e}.selectedOptions`);return a}function H(t){const e=I(t);if(!e.options)return[];const s={};e.options.map(o=>{var a;(a=o.optionValues)==null||a.map(i=>{if(i.firstSelectableVariant){const r=P(i.firstSelectableVariant.selectedOptions);s[r]=i.firstSelectableVariant}})}),e.adjacentVariants.map(o=>{const a=P(o.selectedOptions);s[a]=o});const n=e.selectedOrFirstAvailableVariant;if(n){const o=P(n.selectedOptions);s[o]=n}return Object.values(s)}function M(t){const e=I(t,!0);if(!e.options)return[];const{options:s,selectedOrFirstAvailableVariant:n,adjacentVariants:o,encodedVariantExistence:a,encodedVariantAvailability:i,handle:r}=e,p=n==null?void 0:n.selectedOptions.map(c=>c.name),l=s.filter(c=>p&&p.indexOf(c.name)>=0),f=$(s),R=G(n?[n,...o]:o),S=g(n?n.selectedOptions:[]);return l.map((c,x)=>({...c,optionValues:c.optionValues.map(m=>{var E;const V={...S};V[c.name]=m.name;const K=N(V||[]),h=X(V||[],f).slice(0,x+1),T=j(h,a||""),U=j(h,i||""),O=R[K]||m.firstSelectableVariant;let _={};O&&(_=g(O.selectedOptions||[]));const D=new URLSearchParams(_),A=((E=O==null?void 0:O.product)==null?void 0:E.handle)||r;return{...m,variant:O,handle:A,variantUriQuery:D.toString(),selected:S[c.name]===m.name,exists:T,available:U,isDifferentProduct:A!==r}})}))}export{M as a,H as g,g as m};
