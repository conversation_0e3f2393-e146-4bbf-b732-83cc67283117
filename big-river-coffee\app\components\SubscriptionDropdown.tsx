import {useState} from 'react';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment, ProductVariantFragment} from 'storefrontapi.generated';

interface SubscriptionDropdownProps {
  product: ProductFragment;
  selectedVariant: ProductVariantFragment;
  quantity: number;
}

interface SubscriptionOption {
  id: string;
  name: string;
  description: string;
  sellingPlanId: string;
  savings?: string;
}

export function SubscriptionDropdown({
  product,
  selectedVariant,
  quantity
}: SubscriptionDropdownProps) {
  const { open } = useAside();
  const [selectedOption, setSelectedOption] = useState<string>('onetime');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Define the specific selling plans we want to show
  const subscriptionOptions: SubscriptionOption[] = [
    {
      id: 'onetime',
      name: 'One-time purchase',
      description: 'No commitment',
      sellingPlanId: '',
    },
    {
      id: 'weekly',
      name: 'Weekly',
      description: 'Delivered every week • 15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9581953339',
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: 'Delivered every month • 15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9581986107',
    },
    {
      id: 'every3weeks',
      name: 'Every 3 weeks',
      description: 'Delivered every 3 weeks • 15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9582018875',
    },
    {
      id: 'every6weeks',
      name: 'Every 6 weeks',
      description: 'Delivered every 6 weeks • 15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9582051643',
    }
  ];

  const currentOption = subscriptionOptions.find(option => option.id === selectedOption) || subscriptionOptions[0];

  // Check if product has selling plans
  const hasSellingPlans = product?.sellingPlanGroups?.nodes && product.sellingPlanGroups.nodes.length > 0;

  if (!hasSellingPlans) {
    return (
      <div className="pt-4">
        <AddToCartButton
          disabled={!selectedVariant || !selectedVariant.availableForSale}
          onClick={() => {
            open('cart');
          }}
          lines={
            selectedVariant
              ? [
                  {
                    merchandiseId: selectedVariant.id,
                    quantity: quantity,
                    selectedVariant,
                  },
                ]
              : []
          }
        >
          {selectedVariant?.availableForSale ? 'Add to cart' : 'Sold out'}
        </AddToCartButton>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Subscription Frequency Selector */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium !text-black">Subscription Frequency</h3>
          <span className="text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded-full">
            15% OFF
          </span>
        </div>
        
        {/* Dropdown Selector */}
        <div className="relative">
          <button
            type="button"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 hover:border-gray-400 transition-colors duration-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center">
                  <span className="font-medium !text-black">{currentOption.name}</span>
                </div>
                <span className="text-sm !text-gray-700">{currentOption.description}</span>
              </div>
              <svg
                className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                  isDropdownOpen ? 'transform rotate-180' : ''
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </button>

          {/* Dropdown Options */}
          {isDropdownOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto overscroll-contain">
              {subscriptionOptions.map((option) => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => {
                    setSelectedOption(option.id);
                    setIsDropdownOpen(false);
                  }}
                  className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg ${
                    selectedOption === option.id ? 'bg-army-50 border-l-4 border-army-500' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium !text-black">{option.name}</span>
                      </div>
                      <span className="text-sm !text-gray-700">{option.description}</span>
                    </div>
                    {selectedOption === option.id && (
                      <svg className="w-5 h-5 text-army-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add to Cart Button */}
      <div className="pt-4">
        <AddToCartButton
          disabled={!selectedVariant || !selectedVariant.availableForSale}
          onClick={() => {
            open('cart');
          }}
          lines={
            selectedVariant
              ? [
                  {
                    merchandiseId: selectedVariant.id,
                    quantity: quantity,
                    selectedVariant,
                    ...(currentOption.sellingPlanId && {
                      sellingPlanId: currentOption.sellingPlanId,
                    }),
                  },
                ]
              : []
          }
          className="w-full bg-army-600 hover:bg-army-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 text-white"
        >
          {selectedVariant?.availableForSale ? 'Add to Cart' : 'Sold out'}
        </AddToCartButton>
      </div>
    </div>
  );
}
